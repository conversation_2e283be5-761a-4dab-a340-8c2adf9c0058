/* Roundels - HTML/CSS Implementation */
/* Replaces the problematic SVG with embedded base64 images */

/* Roundels 1 - Flexbox version (309x124) */
.roundels-container {
    display: flex;
    width: 309px;
    height: 124px;
    position: relative;
}

.roundel {
    border-radius: 62px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.roundel-1 {
    width: 129px;
    height: 124px;
    background-image: url('roundel_1.jpg');
    z-index: 3;
}

.roundel-2 {
    width: 130px;
    height: 124px;
    margin-left: -40px;
    background-image: url('roundel_2.jpg');
    z-index: 2;
}

.roundel-3 {
    width: 130px;
    height: 124px;
    margin-left: -40px;
    background-image: url('roundel_3.jpg');
    z-index: 1;
}

/* Responsive design for mobile - Roundels 1 */
@media (max-width: 768px) {
    .roundels-container {
        flex-direction: column;
        width: 130px;
        height: auto;
        align-items: center;
    }

    .roundel-2,
    .roundel-3 {
        margin-left: 0;
        margin-top: -20px;
    }
}

/* Roundels 2 - Flexbox version (307x125) */
.roundels2-container {
    display: flex;
    width: 307px;
    height: 125px;
    position: relative;
}

.roundel2 {
    border-radius: 62.5px;
    background-size: cover;
    background-position: center;
    background-repeat: no-repeat;
    position: relative;
}

.roundel2-1 {
    width: 129px;
    height: 125px;
    background-image: url('roundel_4.jpg');
    z-index: 3;
}

.roundel2-2 {
    width: 129px;
    height: 125px;
    margin-left: -40px;
    background-image: url('roundel_5.jpg');
    z-index: 2;
}

.roundel2-3 {
    width: 129px;
    height: 125px;
    margin-left: -40px;
    background-image: url('roundel_6.jpg');
    z-index: 1;
}

/* Responsive design for mobile - Roundels 2 */
@media (max-width: 768px) {
    .roundels2-container {
        flex-direction: column;
        width: 129px;
        height: auto;
        align-items: center;
    }

    .roundel2-2,
    .roundel2-3 {
        margin-left: 0;
        margin-top: -20px;
    }
}

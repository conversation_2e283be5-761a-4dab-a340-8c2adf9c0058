<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Roundels - HTML/CSS Version</title>
    <style>
        /* 4-image roundels container - 4x1 on desktop, 2x2 on mobile */
        .roundels-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            max-width: 600px;
            justify-content: center;
        }

        .roundel {
            border-radius: 50%;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            width: 120px;
            height: 120px;
            flex-shrink: 0;
        }

        .roundel-1 {
            background-image: url('roundel_1.jpg');
        }

        .roundel-2 {
            background-image: url('roundel_2.jpg');
        }

        .roundel-3 {
            background-image: url('roundel_3.jpg');
        }

        .roundel-4 {
            background-image: url('roundel_4.jpg');
        }

        /* Desktop: 4x1 layout */
        @media (min-width: 769px) {
            .roundels-container {
                flex-wrap: nowrap;
                gap: 15px;
            }
        }

        /* Mobile: 2x2 layout */
        @media (max-width: 768px) {
            .roundels-container {
                max-width: 260px;
                gap: 10px;
            }

            .roundel {
                width: 100px;
                height: 100px;
                flex: 0 0 calc(50% - 5px);
            }
        }

        /* Legacy 3-image overlapping roundels for comparison */
        .roundels-legacy-container {
            display: flex;
            width: 309px;
            height: 124px;
            position: relative;
            margin-top: 40px;
        }

        .roundel-legacy {
            border-radius: 62px;
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            position: relative;
        }

        .roundel-legacy-1 {
            width: 129px;
            height: 124px;
            background-image: url('roundel_1.jpg');
            z-index: 3;
        }

        .roundel-legacy-2 {
            width: 130px;
            height: 124px;
            margin-left: -40px;
            background-image: url('roundel_2.jpg');
            z-index: 2;
        }

        .roundel-legacy-3 {
            width: 130px;
            height: 124px;
            margin-left: -40px;
            background-image: url('roundel_3.jpg');
            z-index: 1;
        }

        /* Legacy mobile responsive */
        @media (max-width: 768px) {
            .roundels-legacy-container {
                flex-direction: column;
                width: 130px;
                height: auto;
                align-items: center;
            }

            .roundel-legacy-2,
            .roundel-legacy-3 {
                margin-left: 0;
                margin-top: -20px;
            }
        }
    </style>
</head>
<body>
    <h2>New 4-Image Roundels (4x1 desktop, 2x2 mobile)</h2>
    <div class="roundels-container">
        <div class="roundel roundel-1"></div>
        <div class="roundel roundel-2"></div>
        <div class="roundel roundel-3"></div>
        <div class="roundel roundel-4"></div>
    </div>

    <h2>Legacy 3-Image Overlapping Roundels (for comparison)</h2>
    <div class="roundels-legacy-container">
        <div class="roundel-legacy roundel-legacy-1"></div>
        <div class="roundel-legacy roundel-legacy-2"></div>
        <div class="roundel-legacy roundel-legacy-3"></div>
    </div>
</body>
</html>

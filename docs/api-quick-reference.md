# EverTrek API Quick Reference

## Public Endpoints (No Authentication)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/index.php?option=com_zenadmin&view=clientconfig&format=json` | GET | Client configuration |
| `/index.php?option=com_zenholidays&view=map&format=json&id={id}` | GET | Holiday map data |
| `/index.php?option=com_zenholidays&task=holiday.get_json&vc={code}` | GET | Holiday by version code |
| `/index.php?option=com_zenholidays&task=getUrlFromCode&code={code}` | GET | Holiday URL from code |
| `/index.php?option=com_zenaccommodations&task=search.results` | GET | Search accommodations |
| `/index.php?option=com_zenaccommodations&task=search.facets` | GET | Accommodation facets |

## Authenticated Endpoints

### Holiday Data Feed (Access Key Required)
```bash
# Header method
curl -H "X-Access-Key: YOUR_KEY" \
     "/index.php?option=com_zenholidays&view=datafeed&format=json&id={id}"

# URL parameter method  
curl "/index.php?option=com_zenholidays&view=datafeed&format=json&id={id}&access_key=YOUR_KEY"
```

### ZenProvider API (HMAC Signature Required)
```bash
curl -X POST \
     -H "X-Request-Signature: {public_key} {timestamp} SHA256:{signature}" \
     -H "Content-Type: application/json" \
     -d '{...}' \
     "/index.php?option=com_zenprovider&view={view}&format=json"
```

**Available ZenProvider Views:**
- `book` - Book holidays/activities
- `packagebooking` - Package bookings
- `accommodationsearch` - Search accommodations
- `description` - API description

## Admin Endpoints (Session Required)

| Endpoint | Method | Description |
|----------|--------|-------------|
| `/administrator/index.php?option=com_zenholidays&view=holidays&format=json` | GET | Holiday search |
| `/index.php?option=com_sppagebuilder&view=ajax&format=json` | POST | Page builder AJAX |
| `/index.php?option=com_sppagebuilder&view=media&format=json` | POST | Media browser |

## Authentication Methods

### 1. Access Key
- **Header:** `X-Access-Key: your_key`
- **URL:** `&access_key=your_key`
- **Used for:** Holiday data feed

### 2. HMAC Signature
- **Header:** `X-Request-Signature: {public_key} {timestamp} SHA256:{signature}`
- **Algorithm:** HMAC-SHA256
- **Message:** `{timestamp}{first_1kb_of_body}`
- **Used for:** ZenProvider API

### 3. Session Cookie
- **Method:** Standard Joomla session
- **Used for:** Admin endpoints, SP Page Builder

## Response Formats

### Success Response
```json
{
  "success": true,
  "data": {...},
  "message": "Optional message"
}
```

### Error Response
```json
{
  "success": false,
  "message": "Error description",
  "data": null
}
```

## Common Parameters

| Parameter | Type | Description |
|-----------|------|-------------|
| `id` | int | Resource ID (holiday, accommodation, etc.) |
| `format` | string | Response format (`json`, `xml`) |
| `access_key` | string | Access key for authenticated endpoints |
| `vc` | string | Version code for holidays |
| `code` | string | Holiday code |

## Quick Test Commands

### Test Client Config
```bash
curl "https://yourdomain.com/index.php?option=com_zenadmin&view=clientconfig&format=json"
```

### Test Holiday Data (with access key)
```bash
curl -H "X-Access-Key: YOUR_KEY" \
     "https://yourdomain.com/index.php?option=com_zenholidays&view=datafeed&format=json&id=1"
```

### Test Accommodation Search
```bash
curl "https://yourdomain.com/index.php?option=com_zenaccommodations&task=search.results"
```

## Error Codes

| Code | Description | Solution |
|------|-------------|----------|
| 403 | Access Denied | Check access key or authentication |
| 404 | Not Found | Verify resource ID exists |
| 500 | Server Error | Check server logs for PHP errors |

## Getting Access

1. **Holiday Data Feed:** Admin → Components → Zen Holidays → Data Feeds
2. **ZenProvider API:** Admin → Components → Zen Provider → Credentials
3. **Admin Access:** Valid Joomla admin session required

For detailed documentation, see `api-endpoints-documentation.md`.

# EverTrek API Endpoints Documentation

This document provides comprehensive information about all available API endpoints in your Joomla 3.10 site for programmatic content access.

## Table of Contents

1. [Public JSON Endpoints](#public-json-endpoints)
2. [Authenticated Endpoints](#authenticated-endpoints)
3. [Admin/Backend Endpoints](#adminbackend-endpoints)
4. [ZenProvider API (Advanced)](#zenprovider-api-advanced)
5. [Authentication Methods](#authentication-methods)
6. [Usage Examples](#usage-examples)

---

## Public JSON Endpoints

### 1. Client Configuration
**Endpoint:** `GET /index.php?option=com_zenadmin&view=clientconfig&format=json`

**Description:** Returns client-side configuration data including user information and currency settings.

**Authentication:** None required

**Response Format:**
```json
{
  "user": {
    "id": 123,
    "sid": "session_id_here",
    "ccy": "GBP"
  }
}
```

### 2. Holiday Map Data
**Endpoint:** `GET /index.php?option=com_zenholidays&view=map&format=json&id={holiday_id}`

**Description:** Returns holiday location and map data with color configuration.

**Authentication:** None required

**Parameters:**
- `id` (required): Holiday ID

**Response:** Holiday item with map colors and location data

### 3. SP Page Builder AJAX
**Endpoint:** `POST /index.php?option=com_sppagebuilder&view=ajax&format=json`

**Description:** Various AJAX operations for page building functionality.

**Authentication:** Requires valid Joomla session and edit permissions

**Parameters:**
- `callback`: Action to perform (e.g., 'get-page-data')

---

## Authenticated Endpoints

### 1. Holiday Data Feed
**Endpoint:** `GET /index.php?option=com_zenholidays&view=datafeed&format=json&id={holiday_id}`

**Description:** Returns comprehensive holiday data in JSON format for external systems.

**Authentication:** Access key required (see [Authentication Methods](#authentication-methods))

**Parameters:**
- `id` (required): Holiday ID
- `access_key` (required): Valid access key

**Headers:**
- `X-Access-Key: your_access_key_here` (alternative to URL parameter)

**Response:** Complete holiday data including pricing, itinerary, and metadata

### 2. Holiday by Version Code
**Endpoint:** `GET /index.php?option=com_zenholidays&task=holiday.get_json&vc={version_code}`

**Description:** Retrieve holiday data using version code instead of ID.

**Authentication:** None required

**Parameters:**
- `vc` (required): Holiday version code

### 3. Holiday URL from Code
**Endpoint:** `GET /index.php?option=com_zenholidays&task=getUrlFromCode&code={holiday_code}`

**Description:** Get the full URL for a holiday using its code.

**Authentication:** None required

**Parameters:**
- `code` (required): Holiday code

**Response:**
```json
{
  "url": "/path/to/holiday"
}
```

---

## Admin/Backend Endpoints

### 1. Holiday Search (Admin)
**Endpoint:** `GET /administrator/index.php?option=com_zenholidays&view=holidays&format=json`

**Description:** Search holidays by name for admin interfaces (e.g., Select2 dropdowns).

**Authentication:** Admin session required

**Response:**
```json
[
  {
    "id": 123,
    "text": "Holiday Name (CODE)"
  }
]
```

---

## ZenProvider API (Advanced)

The ZenProvider API uses sophisticated HMAC-SHA256 signature authentication for external system integration.

### Authentication Header
```
X-Request-Signature: {public_key} {timestamp} {signature}
```

Where:
- `public_key`: Your assigned public key
- `timestamp`: Unix timestamp (must be within 5 minutes)
- `signature`: `SHA256:{hmac_sha256_signature}`

### Available Endpoints

#### 1. Product Booking
**Endpoint:** `POST /index.php?option=com_zenprovider&view=book&format=json`

**Description:** Book holidays or activities through the provider API.

**Request Body:**
```json
{
  "productId": "holiday:123",
  "customerData": {...},
  "bookingDetails": {...}
}
```

#### 2. Package Booking
**Endpoint:** `POST /index.php?option=com_zenprovider&view=packagebooking&format=json`

**Description:** Create package bookings with availability checking.

#### 3. Accommodation Search
**Endpoint:** `POST /index.php?option=com_zenprovider&view=accommodationsearch&format=json`

**Description:** Search for accommodation availability and pricing.

#### 4. API Description
**Endpoint:** `GET /index.php?option=com_zenprovider&view=description&format=json`

**Description:** Returns API capabilities and endpoint descriptions.

---

## Authentication Methods

### 1. Access Key Authentication (Holiday Data Feed)

**Method 1 - HTTP Header:**
```http
GET /index.php?option=com_zenholidays&view=datafeed&format=json&id=123
X-Access-Key: your_access_key_here
```

**Method 2 - URL Parameter:**
```http
GET /index.php?option=com_zenholidays&view=datafeed&format=json&id=123&access_key=your_access_key_here
```

### 2. HMAC Signature Authentication (ZenProvider)

**Step 1:** Generate timestamp
```javascript
const timestamp = Math.floor(Date.now() / 1000);
```

**Step 2:** Create signature
```javascript
const body = JSON.stringify(requestData);
const message = timestamp + body.substring(0, 1024); // First 1KB only
const signature = crypto.createHmac('sha256', privateKey).update(message).digest('hex');
```

**Step 3:** Set header
```http
X-Request-Signature: {publicKey} {timestamp} SHA256:{signature}
```

### 3. Session Authentication (Admin/SP Page Builder)

Requires valid Joomla session cookie and appropriate user permissions.

---

## Usage Examples

### Example 1: Get Holiday Data
```bash
curl -H "X-Access-Key: your_key_here" \
     "https://yourdomain.com/index.php?option=com_zenholidays&view=datafeed&format=json&id=123"
```

### Example 2: Get Client Configuration
```bash
curl "https://yourdomain.com/index.php?option=com_zenadmin&view=clientconfig&format=json"
```

### Example 3: Search Accommodations
```bash
curl "https://yourdomain.com/index.php?option=com_zenaccommodations&task=search.results"
```

### Example 4: Get Accommodation Facets
```bash
curl "https://yourdomain.com/index.php?option=com_zenaccommodations&task=search.facets"
```

---

## Additional Endpoints

### Accommodation Search
**Endpoint:** `GET /index.php?option=com_zenaccommodations&task=search.results`

**Description:** Search for accommodations with filtering capabilities.

**Authentication:** None required

**Response:** JSON array of accommodation results

### Accommodation Facets
**Endpoint:** `GET /index.php?option=com_zenaccommodations&task=search.facets`

**Description:** Get available search facets/filters for accommodations.

**Authentication:** None required

**Response:** JSON object with available facets and filter options

### SP Page Builder Media
**Endpoint:** `POST /index.php?option=com_sppagebuilder&view=media&format=json`

**Description:** Browse and manage media files for page building.

**Authentication:** Valid Joomla session required

**Parameters:**
- `layout`: 'browse', 'modal', or 'folders'
- `date`: Filter by date
- `start`: Pagination start
- `search`: Search term

### OSMap XML Sitemap
**Endpoint:** `GET /index.php?option=com_osmap&view=xml&format=xml&id={sitemap_id}`

**Description:** Generate XML sitemaps for SEO purposes.

**Authentication:** None required

**Parameters:**
- `id` (required): Sitemap ID

---

## Error Responses

### Access Denied (403)
```json
{
  "success": false,
  "message": "Access Denied",
  "data": null
}
```

### Invalid Access Key
```json
{
  "success": false,
  "message": "COM_ZENHOLIDAYS_DATAFEED_REJECT_INVALID_KEY"
}
```

### Authentication Expired
```json
{
  "success": false,
  "message": "Authentication has expired"
}
```

---

## Setting Up Access

### Holiday Data Feed Access Keys

Access keys for the holiday data feed are managed in the Joomla admin panel:

1. **Admin Location:** Components → Zen Holidays → Data Feeds
2. **Create New Feed:** Add a new data feed consumer with a unique access key
3. **Key Format:** Alphanumeric string (e.g., `abc123def456`)
4. **Status:** Ensure the feed is published/enabled

### ZenProvider API Credentials

ZenProvider credentials use public/private key pairs:

1. **Admin Location:** Components → Zen Provider → Credentials
2. **Key Generation:** System generates public/private key pairs
3. **Public Key:** Used in the X-Request-Signature header
4. **Private Key:** Used to sign requests (keep secure)

### Testing Your Setup

#### Test Public Endpoints First
```bash
# Test client config (no auth required)
curl "https://yourdomain.com/index.php?option=com_zenadmin&view=clientconfig&format=json"
```

#### Test Holiday Data Feed
```bash
# Replace YOUR_ACCESS_KEY with your actual key
curl -H "X-Access-Key: YOUR_ACCESS_KEY" \
     "https://yourdomain.com/index.php?option=com_zenholidays&view=datafeed&format=json&id=1"
```

#### Test Holiday by Version Code
```bash
# Replace VERSION_CODE with an actual holiday version code
curl "https://yourdomain.com/index.php?option=com_zenholidays&task=holiday.get_json&vc=VERSION_CODE"
```

---

## Rate Limiting and Best Practices

### Rate Limiting
- **Holiday Data Feed:** No explicit rate limiting, but access is logged
- **ZenProvider API:** 5-minute timestamp window for security
- **Public Endpoints:** Standard Joomla rate limiting applies

### Best Practices
1. **Cache Responses:** Cache API responses when possible to reduce server load
2. **Error Handling:** Always implement proper error handling for 403/404/500 responses
3. **Secure Keys:** Never expose access keys or private keys in client-side code
4. **Timestamp Sync:** Ensure server time is synchronized for ZenProvider API
5. **Request Logging:** Monitor your API usage through server logs

---

## Troubleshooting

### Common Issues

#### "Access Denied" (403)
- Check if access key is valid and active
- Verify the key is correctly formatted in header or URL
- Ensure the data feed consumer is published

#### "Authentication has expired"
- ZenProvider API: Check timestamp is within 5-minute window
- Verify server time synchronization
- Regenerate signature with current timestamp

#### "Invalid signature"
- Verify private key is correct
- Check signature generation algorithm
- Ensure request body matches what was signed

#### Empty/No Response
- Check if the requested resource (holiday ID) exists
- Verify component is installed and enabled
- Check server error logs for PHP errors

### Debug Mode
Add `&debug=1` to any endpoint URL to get additional debug information (admin access required).

---

## Next Steps

1. **Request Access Keys:** Contact your administrator to obtain access keys for the holiday data feed
2. **Set Up ZenProvider Credentials:** If you need advanced API access, request ZenProvider credentials
3. **Test Endpoints:** Start with the public endpoints to familiarize yourself with the response formats
4. **Implement Authentication:** Choose the appropriate authentication method for your use case
5. **Build Integration:** Use the documented endpoints to build your integration

For additional support or to request new API endpoints, please contact your development team.

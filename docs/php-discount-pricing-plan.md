# PHP Template-Based Discount Display Solution Plan

## Table of Contents
1. [Overview](#overview)
2. [Data Storage & Configuration](#data-storage--configuration)
3. [PHP Helper Class Creation](#php-helper-class-creation)
4. [Template Integration Points](#template-integration-points)
5. [Template Modification Strategy](#template-modification-strategy)
6. [CSS Styling](#css-styling)
7. [URL Matching Logic](#url-matching-logic)
8. [Implementation Phases](#implementation-phases)
9. [Data Format & Validation](#data-format--validation)
10. [Performance Considerations](#performance-considerations)
11. [Compatibility & Migration](#compatibility--migration)
12. [Testing Strategy](#testing-strategy)

## Overview

This document outlines a comprehensive plan for implementing a PHP template-based solution to display discounted prices across all pages. The system will take product prices, add back a discount amount, show the original price crossed out, and display the discounted price as the selling price.

**Key Requirements:**
- Display crossed-out original prices with discounted selling prices
- Support both full payment and monthly payment pricing
- Integrate seamlessly with existing template system
- Use Joomla copy items or modules for configuration
- Match products by URL to closest price container

## Data Storage & Configuration

### Option A: Joomla Module Approach
- Create a new module `mod_zen_price_discounts` 
- Store discount data in module parameters as JSON
- Module can be assigned to specific pages or globally
- Easy to manage through Joomla admin interface

### Option B: Copy Item Approach (Recommended)
- Create a copy item with category "price-discounts"
- Store discount data in the `content` field as JSON
- Use `ZenModelHelper::getCopyItemsByAlias()` to retrieve
- More flexible, can be assigned per holiday/page
- Follows existing codebase patterns

**Rationale for Copy Item Approach:**
- Consistent with existing codebase patterns
- Better version control and content management
- Supports per-trip customization
- Easier integration with existing helper functions

## PHP Helper Class Creation

Create `libraries/mrzen/helpers/ZenPriceDiscountHelper.php`:

```php
class ZenPriceDiscountHelper 
{
    private static $discountData = null;
    
    /**
     * Load discount configuration from copy item or module
     * @param string $extension Extension name (e.g., 'com_zenholidays')
     * @param int $id Item ID
     * @return array Discount configuration data
     */
    public static function loadDiscountData($extension = 'global', $id = 0)
    
    /**
     * Check if current URL/page has discount
     * @param string $url URL to check (optional, uses current if null)
     * @return bool True if discount available
     */
    public static function hasDiscount($url = null)
    
    /**
     * Get discount amount for current URL/page  
     * @param string $url URL to check (optional, uses current if null)
     * @return float Discount amount or 0 if none
     */
    public static function getDiscountAmount($url = null)
    
    /**
     * Format price with discount display
     * @param float $originalPrice Original price value
     * @param float $discountAmount Discount amount to add back
     * @param string $currency Currency symbol
     * @param string $suffix Price suffix (e.g., 'pp')
     * @return string Formatted HTML with crossed-out and discounted prices
     */
    public static function formatDiscountedPrice($originalPrice, $discountAmount, $currency = '£', $suffix = 'pp')
    
    /**
     * Generate crossed-out price HTML
     * @param float $originalPrice Original price value
     * @param string $url URL to match against (optional)
     * @param string $currency Currency symbol
     * @param string $suffix Price suffix
     * @return string HTML output with discount formatting or original price
     */
    public static function renderDiscountedPrice($originalPrice, $url = null, $currency = '£', $suffix = 'pp')
}
```

## Template Integration Points

### Primary Integration Locations:

1. **Holiday Detail Pages** (`templates/zenbase/html/com_zenholidays/holiday/`)
   - `default_hero.php` - Main price display in hero section
   - `default_dates-prices.php` - Date-specific pricing tables

2. **Search Results** (`templates/zenbase/html/com_zenholidays/search/`)
   - `default_holidays.php` - Search result cards with pricing

3. **Related Trips** (`templates/zenbase/html/partials/related.php`)
   - Related holiday cards with pricing information

4. **Module Templates** 
   - `modules/mod_zenelasticholidays/tmpl/default_date.php`
   - Any other pricing display modules

### Secondary Integration Points:
- Plugin templates (`plugins/content/holiday/tmpl/`)
- SP Page Builder pricing components
- Any custom pricing displays

## Template Modification Strategy

### Before (Current Implementation):
```php
<?php echo $fromPrice->currency_symbol . number_format($fromPrice->value) . 'pp'; ?>
```

### After (With Discount Support):
```php
<?php 
$currentUrl = ZenURLHelper::getCurrentPageUrl(); // or derive from context
echo ZenPriceDiscountHelper::renderDiscountedPrice(
    $fromPrice->value, 
    $currentUrl, 
    $fromPrice->currency_symbol, 
    'pp'
); 
?>
```

### Monthly Payment Integration:
```php
<?php 
$monthlyPrice = ($fromPrice->value - 200) / 18; // Existing calculation
$currentUrl = ZenURLHelper::getCurrentPageUrl();
echo ZenPriceDiscountHelper::renderDiscountedPrice(
    $monthlyPrice, 
    $currentUrl, 
    $fromPrice->currency_symbol, 
    '/18 months'
); 
?>
```

## CSS Styling

Add to `templates/zenbase/css/overrides.css`:

```css
.zen-price--discounted {
    display: inline-block;
}

.zen-price--was {
    text-decoration: line-through;
    opacity: 0.5;
    margin-right: 8px;
    font-size: 0.9em;
    color: #666;
}

.zen-price--now {
    color: #fe7720;
    font-weight: 700;
}

.zen-price--monthly .zen-price--was,
.zen-price--monthly .zen-price--now {
    display: inline-block;
}

/* Responsive adjustments */
@media (max-width: 768px) {
    .zen-price--was {
        font-size: 0.85em;
        margin-right: 6px;
    }
}
```

## URL Matching Logic

### URL Resolution Strategy:
1. **Current Page Detection**: Use `JUri::getInstance()` to get current URL
2. **URL Normalization**: Remove domain, trailing slashes, query parameters
3. **Pattern Matching**: Support both exact matches and pattern matching
4. **Holiday Context**: Handle holiday aliases vs full URLs
5. **Fallback Logic**: Graceful degradation when no match found

### URL Context Detection Methods:
- **Holiday Pages**: Use holiday alias/URL from route
- **Search Results**: Extract from individual holiday data objects
- **Related Trips**: Use individual trip URLs from database
- **Module Displays**: Context-aware URL detection based on current page

### Implementation Example:
```php
public static function normalizeUrl($url) {
    // Remove protocol and domain
    $url = preg_replace('#^https?://[^/]+#', '', $url);
    // Remove trailing slash
    $url = rtrim($url, '/');
    // Remove query parameters
    $url = strtok($url, '?');
    return $url;
}

public static function matchUrl($targetUrl, $configUrl) {
    $normalizedTarget = self::normalizeUrl($targetUrl);
    $normalizedConfig = self::normalizeUrl($configUrl);
    
    // Exact match
    if ($normalizedTarget === $normalizedConfig) {
        return true;
    }
    
    // Pattern matching for wildcards (future enhancement)
    // return fnmatch($normalizedConfig, $normalizedTarget);
    
    return false;
}
```

## Implementation Phases

### Phase 1: Core Infrastructure
**Duration: 2-3 days**

- Create `ZenPriceDiscountHelper` class with core methods
- Implement copy item data loading functionality
- Create URL matching and normalization logic
- Add basic CSS styling for discount display
- Unit testing for helper class methods

**Deliverables:**
- `libraries/mrzen/helpers/ZenPriceDiscountHelper.php`
- Basic CSS additions to `templates/zenbase/css/overrides.css`
- Test copy item with sample discount data

### Phase 2: Template Integration
**Duration: 3-4 days**

- Modify holiday detail page templates (`default_hero.php`, `default_dates-prices.php`)
- Update search result templates (`default_holidays.php`)
- Integrate with related trips display (`related.php`)
- Test with sample discount data across different page types

**Deliverables:**
- Updated holiday template files
- Updated search template files
- Updated related trips partial
- Integration testing results

### Phase 3: Module Integration
**Duration: 2-3 days**

- Update pricing modules (`mod_zenelasticholidays`)
- Handle Angular/JavaScript price displays compatibility
- Ensure compatibility with existing pricing system
- Add caching for performance optimization

**Deliverables:**
- Updated module templates
- JavaScript compatibility layer
- Performance optimization implementation
- Caching strategy documentation

### Phase 4: Admin Interface & Polish
**Duration: 2-3 days**

- Create copy item management interface/documentation
- Add validation for discount data format
- Implement preview functionality
- Documentation and training materials
- Final testing and bug fixes

**Deliverables:**
- Admin documentation
- Validation rules implementation
- Preview functionality
- User training materials
- Final testing report

## Data Format & Validation

### Copy Item Content Format:
```json
{
  "discounts": [
    {"url": "/holidays/kilimanjaro-the-long-way", "discount": 400.0},
    {"url": "/holidays/machu-picchu-via-inca-trail", "discount": 100.0},
    {"url": "/destinations/asia/nepal/everest-base-camp-expedition", "discount": 400.0}
  ],
  "active": true,
  "valid_from": "2024-01-01",
  "valid_until": "2024-12-31",
  "currency": "GBP",
  "notes": "November 2024 discount campaign"
}
```

### Validation Rules:
- **URL Format**: Must start with `/` and contain valid URL characters
- **Discount Amount**: Must be positive number (float/integer)
- **Date Range**: Valid date format (YYYY-MM-DD), valid_until >= valid_from
- **JSON Structure**: Valid JSON with required fields
- **Currency**: Valid 3-letter currency code (optional, defaults to system currency)

### Copy Item Configuration:
- **Category**: "price-discounts"
- **Extension**: "global" or specific component (e.g., "com_zenholidays")
- **Extension ID**: 0 for global, specific ID for targeted discounts
- **State**: Published (1) for active discounts

## Performance Considerations

### Caching Strategy:
- **Data Caching**: Use `ZenCache` class to cache discount configuration
- **Cache Key**: Based on extension, ID, and last modified date
- **Cache Duration**: 1 hour for discount data, cleared on copy item update
- **Memory Caching**: Store processed discount data in static variables

### URL Optimization:
- **Pre-processing**: Normalize URLs once and store in memory
- **Lookup Optimization**: Use associative arrays for O(1) URL lookups
- **Lazy Loading**: Only load discount data when `hasDiscount()` is called

### Template Efficiency:
- **Minimal Queries**: Avoid additional database queries in templates
- **Conditional Loading**: Only process discounts when discount data exists
- **Fallback Performance**: Ensure original pricing display remains fast

### Implementation Example:
```php
private static $urlCache = [];
private static $discountCache = null;

public static function getDiscountAmount($url = null) {
    if (self::$discountCache === null) {
        self::loadDiscountData();
    }

    $normalizedUrl = self::normalizeUrl($url ?: self::getCurrentUrl());

    if (isset(self::$urlCache[$normalizedUrl])) {
        return self::$urlCache[$normalizedUrl];
    }

    // Process and cache result
    $discount = self::findDiscountForUrl($normalizedUrl);
    self::$urlCache[$normalizedUrl] = $discount;

    return $discount;
}
```

## Compatibility & Migration

### JavaScript Integration:
- **Coexistence**: PHP takes precedence for server-rendered content
- **Dynamic Updates**: JavaScript handles AJAX-loaded content updates
- **Fallback**: Maintain existing `misc/pricing-and-slashes.js` for dynamic scenarios
- **Data Sharing**: Share discount configuration between PHP and JavaScript

### Migration Strategy:
- **Gradual Rollout**: Implement per template file, not all at once
- **Feature Flags**: Use copy item state to enable/disable discounts
- **Rollback Plan**: Easy to revert template changes if issues arise
- **Data Migration**: Convert existing JavaScript discount data to copy item format

### Backward Compatibility:
- **Template Fallback**: If helper class fails, display original price
- **Graceful Degradation**: System continues to work without discount data
- **Version Support**: Compatible with existing Joomla 3.x infrastructure

## Testing Strategy

### Test Cases:

#### Functional Testing:
1. **Holiday Detail Pages**
   - Pages with discounts show crossed-out original price
   - Pages without discounts show normal pricing
   - Monthly payment calculations work correctly with discounts
   - Currency symbols display correctly

2. **Search Results**
   - Mixed results (some with/without discounts) display correctly
   - Search result pricing matches detail page pricing
   - Responsive display works on mobile devices

3. **Edge Cases**
   - Invalid discount data gracefully falls back to original pricing
   - Missing copy items don't break page rendering
   - Large discount amounts display properly
   - Zero or negative discounts are handled appropriately

#### Performance Testing:
- Page load times with/without discount system
- Memory usage with large discount datasets
- Cache effectiveness and invalidation
- Database query impact analysis

#### Cross-Browser Testing:
- Desktop browsers (Chrome, Firefox, Safari, Edge)
- Mobile browsers (iOS Safari, Android Chrome)
- Responsive design across different screen sizes

#### Integration Testing:
- Compatibility with existing pricing system
- JavaScript/PHP interaction scenarios
- Module and component integration
- Currency conversion with discounts

### Testing Data:
```json
{
  "discounts": [
    {"url": "/holidays/test-trip-1", "discount": 100.0},
    {"url": "/holidays/test-trip-2", "discount": 500.0},
    {"url": "/holidays/test-trip-3", "discount": 50.0}
  ],
  "active": true,
  "valid_from": "2024-01-01",
  "valid_until": "2024-12-31"
}
```

---

## Change Control

| Version | Date | Author | Changes |
|---------|------|--------|---------|
| 1.0 | 2024-06-30 | System | Initial plan creation |

---

*This document provides a comprehensive roadmap for implementing PHP template-based discount pricing functionality. The plan prioritizes maintainability, performance, and seamless integration with the existing codebase.*
```
